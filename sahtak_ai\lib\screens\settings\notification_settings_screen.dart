import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../services/notification_service.dart';

/// شاشة إعدادات الإشعارات
class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  
  // إعدادات الإشعارات
  bool _workoutReminders = true;
  bool _mealReminders = true;
  bool _weightTracking = true;
  bool _waterReminders = true;
  bool _motivationalMessages = true;
  
  // أوقات الإشعارات
  TimeOfDay _workoutTime = const TimeOfDay(hour: 18, minute: 0);
  TimeOfDay _breakfastTime = const TimeOfDay(hour: 8, minute: 0);
  TimeOfDay _lunchTime = const TimeOfDay(hour: 13, minute: 0);
  TimeOfDay _dinnerTime = const TimeOfDay(hour: 19, minute: 0);
  TimeOfDay _weightTrackingTime = const TimeOfDay(hour: 7, minute: 0);
  
  // أيام التمارين
  List<bool> _workoutDays = List.filled(7, false);
  final List<String> _dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  
  // إعدادات الماء
  int _waterStartHour = 8;
  int _waterEndHour = 22;
  int _waterInterval = 2;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    setState(() {
      _workoutReminders = prefs.getBool('workout_reminders') ?? true;
      _mealReminders = prefs.getBool('meal_reminders') ?? true;
      _weightTracking = prefs.getBool('weight_tracking') ?? true;
      _waterReminders = prefs.getBool('water_reminders') ?? true;
      _motivationalMessages = prefs.getBool('motivational_messages') ?? true;
      
      // تحميل الأوقات
      final workoutHour = prefs.getInt('workout_hour') ?? 18;
      final workoutMinute = prefs.getInt('workout_minute') ?? 0;
      _workoutTime = TimeOfDay(hour: workoutHour, minute: workoutMinute);
      
      final breakfastHour = prefs.getInt('breakfast_hour') ?? 8;
      final breakfastMinute = prefs.getInt('breakfast_minute') ?? 0;
      _breakfastTime = TimeOfDay(hour: breakfastHour, minute: breakfastMinute);
      
      final lunchHour = prefs.getInt('lunch_hour') ?? 13;
      final lunchMinute = prefs.getInt('lunch_minute') ?? 0;
      _lunchTime = TimeOfDay(hour: lunchHour, minute: lunchMinute);
      
      final dinnerHour = prefs.getInt('dinner_hour') ?? 19;
      final dinnerMinute = prefs.getInt('dinner_minute') ?? 0;
      _dinnerTime = TimeOfDay(hour: dinnerHour, minute: dinnerMinute);
      
      final weightHour = prefs.getInt('weight_hour') ?? 7;
      final weightMinute = prefs.getInt('weight_minute') ?? 0;
      _weightTrackingTime = TimeOfDay(hour: weightHour, minute: weightMinute);
      
      // تحميل أيام التمارين
      for (int i = 0; i < 7; i++) {
        _workoutDays[i] = prefs.getBool('workout_day_$i') ?? false;
      }
      
      // إعدادات الماء
      _waterStartHour = prefs.getInt('water_start_hour') ?? 8;
      _waterEndHour = prefs.getInt('water_end_hour') ?? 22;
      _waterInterval = prefs.getInt('water_interval') ?? 2;
    });
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // حفظ الإعدادات العامة
    await prefs.setBool('workout_reminders', _workoutReminders);
    await prefs.setBool('meal_reminders', _mealReminders);
    await prefs.setBool('weight_tracking', _weightTracking);
    await prefs.setBool('water_reminders', _waterReminders);
    await prefs.setBool('motivational_messages', _motivationalMessages);
    
    // حفظ الأوقات
    await prefs.setInt('workout_hour', _workoutTime.hour);
    await prefs.setInt('workout_minute', _workoutTime.minute);
    await prefs.setInt('breakfast_hour', _breakfastTime.hour);
    await prefs.setInt('breakfast_minute', _breakfastTime.minute);
    await prefs.setInt('lunch_hour', _lunchTime.hour);
    await prefs.setInt('lunch_minute', _lunchTime.minute);
    await prefs.setInt('dinner_hour', _dinnerTime.hour);
    await prefs.setInt('dinner_minute', _dinnerTime.minute);
    await prefs.setInt('weight_hour', _weightTrackingTime.hour);
    await prefs.setInt('weight_minute', _weightTrackingTime.minute);
    
    // حفظ أيام التمارين
    for (int i = 0; i < 7; i++) {
      await prefs.setBool('workout_day_$i', _workoutDays[i]);
    }
    
    // حفظ إعدادات الماء
    await prefs.setInt('water_start_hour', _waterStartHour);
    await prefs.setInt('water_end_hour', _waterEndHour);
    await prefs.setInt('water_interval', _waterInterval);
    
    // تطبيق الإعدادات
    await _applyNotificationSettings();
    
    AppHelpers.showSuccessMessage(context, 'تم حفظ الإعدادات بنجاح');
  }

  /// تطبيق إعدادات الإشعارات
  Future<void> _applyNotificationSettings() async {
    // إلغاء جميع الإشعارات السابقة
    await _notificationService.cancelAllNotifications();
    
    // جدولة إشعارات التمارين
    if (_workoutReminders) {
      final workoutDays = <int>[];
      for (int i = 0; i < 7; i++) {
        if (_workoutDays[i]) {
          workoutDays.add(i + 1); // تحويل إلى 1-7
        }
      }
      
      if (workoutDays.isNotEmpty) {
        await _notificationService.scheduleWorkoutReminders(
          workoutDays: workoutDays,
          hour: _workoutTime.hour,
          minute: _workoutTime.minute,
        );
      }
    }
    
    // جدولة إشعارات الوجبات
    if (_mealReminders) {
      await _notificationService.scheduleMealReminders(
        mealTimes: [
          {
            'name': 'الإفطار',
            'hour': _breakfastTime.hour,
            'minute': _breakfastTime.minute,
          },
          {
            'name': 'الغداء',
            'hour': _lunchTime.hour,
            'minute': _lunchTime.minute,
          },
          {
            'name': 'العشاء',
            'hour': _dinnerTime.hour,
            'minute': _dinnerTime.minute,
          },
        ],
      );
    }
    
    // جدولة إشعار تسجيل الوزن
    if (_weightTracking) {
      await _notificationService.scheduleWeightTrackingReminder(
        hour: _weightTrackingTime.hour,
        minute: _weightTrackingTime.minute,
      );
    }
    
    // جدولة إشعارات الماء
    if (_waterReminders) {
      await _notificationService.scheduleWaterReminders(
        startHour: _waterStartHour,
        endHour: _waterEndHour,
        intervalHours: _waterInterval,
      );
    }
  }

  /// اختيار الوقت
  Future<void> _selectTime(TimeOfDay currentTime, Function(TimeOfDay) onTimeSelected) async {
    final time = await showTimePicker(
      context: context,
      initialTime: currentTime,
    );
    
    if (time != null) {
      onTimeSelected(time);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إشعارات التمارين
            _buildSectionHeader('إشعارات التمارين'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('تذكير التمارين'),
                    subtitle: const Text('تذكيرك بموعد التمرين'),
                    value: _workoutReminders,
                    onChanged: (value) => setState(() => _workoutReminders = value),
                  ),
                  
                  if (_workoutReminders) ...[
                    ListTile(
                      title: const Text('وقت التذكير'),
                      subtitle: Text(_workoutTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(_workoutTime, (time) {
                        setState(() => _workoutTime = time);
                      }),
                    ),
                    
                    const Divider(),
                    
                    Padding(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'أيام التمارين',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          Wrap(
                            spacing: 8,
                            children: List.generate(7, (index) {
                              return FilterChip(
                                label: Text(_dayNames[index]),
                                selected: _workoutDays[index],
                                onSelected: (selected) {
                                  setState(() => _workoutDays[index] = selected);
                                },
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // إشعارات الوجبات
            _buildSectionHeader('إشعارات الوجبات'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('تذكير الوجبات'),
                    subtitle: const Text('تذكيرك بمواعيد الوجبات'),
                    value: _mealReminders,
                    onChanged: (value) => setState(() => _mealReminders = value),
                  ),
                  
                  if (_mealReminders) ...[
                    ListTile(
                      title: const Text('الإفطار'),
                      subtitle: Text(_breakfastTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(_breakfastTime, (time) {
                        setState(() => _breakfastTime = time);
                      }),
                    ),
                    
                    ListTile(
                      title: const Text('الغداء'),
                      subtitle: Text(_lunchTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(_lunchTime, (time) {
                        setState(() => _lunchTime = time);
                      }),
                    ),
                    
                    ListTile(
                      title: const Text('العشاء'),
                      subtitle: Text(_dinnerTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(_dinnerTime, (time) {
                        setState(() => _dinnerTime = time);
                      }),
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // إشعارات أخرى
            _buildSectionHeader('إشعارات أخرى'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('تذكير تسجيل الوزن'),
                    subtitle: const Text('تذكيرك بتسجيل الوزن أسبوعياً'),
                    value: _weightTracking,
                    onChanged: (value) => setState(() => _weightTracking = value),
                  ),
                  
                  if (_weightTracking)
                    ListTile(
                      title: const Text('وقت التذكير'),
                      subtitle: Text(_weightTrackingTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(_weightTrackingTime, (time) {
                        setState(() => _weightTrackingTime = time);
                      }),
                    ),
                  
                  SwitchListTile(
                    title: const Text('تذكير شرب الماء'),
                    subtitle: const Text('تذكيرك بشرب الماء بانتظام'),
                    value: _waterReminders,
                    onChanged: (value) => setState(() => _waterReminders = value),
                  ),
                  
                  SwitchListTile(
                    title: const Text('رسائل تحفيزية'),
                    subtitle: const Text('رسائل تحفيزية لمساعدتك على الاستمرار'),
                    value: _motivationalMessages,
                    onChanged: (value) => setState(() => _motivationalMessages = value),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // اختبار الإشعارات
            Card(
              child: ListTile(
                title: const Text('اختبار الإشعارات'),
                subtitle: const Text('إرسال إشعار تجريبي'),
                leading: const Icon(Icons.notifications_active),
                onTap: () async {
                  await _notificationService.showNotification(
                    title: 'إشعار تجريبي',
                    body: 'هذا إشعار تجريبي من تطبيق صحتك AI',
                  );
                  AppHelpers.showSuccessMessage(context, 'تم إرسال الإشعار التجريبي');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عنوان القسم
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }
}
