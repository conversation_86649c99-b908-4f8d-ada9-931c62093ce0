import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../models/progress_model.dart';

/// شاشة تتبع التقدم مع الرسوم البيانية
class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ProgressStats? _progressStats;
  ChartData? _chartData;
  bool _isLoading = true;
  String _selectedPeriod = '30'; // آخر 30 يوم

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadProgressData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات التقدم
  Future<void> _loadProgressData() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();

      if (authService.currentUserId != null) {
        // تحميل إحصائيات التقدم
        final stats = await firebaseService.getProgressStats(authService.currentUserId!);

        // تحميل بيانات الرسم البياني
        final endDate = DateTime.now();
        final startDate = endDate.subtract(Duration(days: int.parse(_selectedPeriod)));
        final chartData = await firebaseService.getChartData(
          authService.currentUserId!,
          startDate,
          endDate,
        );

        if (mounted) {
          setState(() {
            _progressStats = stats;
            _chartData = chartData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        AppHelpers.showErrorMessage(context, 'فشل في تحميل بيانات التقدم: $e');
      }
    }
  }

  /// إضافة قياس وزن جديد
  Future<void> _addWeightEntry() async {
    final weightController = TextEditingController();
    final notesController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة قياس وزن'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: weightController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الوزن (كجم)',
                hintText: 'مثال: 70.5',
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أي ملاحظات إضافية',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (weightController.text.isNotEmpty) {
                try {
                  final authService = Provider.of<AuthService>(context, listen: false);
                  final firebaseService = FirebaseService();

                  final entry = WeightEntry(
                    id: 'weight_${DateTime.now().millisecondsSinceEpoch}',
                    userId: authService.currentUserId!,
                    weight: double.parse(weightController.text),
                    date: DateTime.now(),
                    notes: notesController.text.isNotEmpty ? notesController.text : null,
                    createdAt: DateTime.now(),
                  );

                  await firebaseService.saveWeightEntry(entry);
                  Navigator.of(context).pop(true);
                } catch (e) {
                  AppHelpers.showErrorMessage(context, 'فشل في حفظ القياس: $e');
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result == true) {
      _loadProgressData();
      AppHelpers.showSuccessMessage(context, 'تم حفظ القياس بنجاح');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تتبع التقدم'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
            Tab(text: 'الوزن', icon: Icon(Icons.monitor_weight)),
            Tab(text: 'التمارين', icon: Icon(Icons.fitness_center)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addWeightEntry,
          ),
          PopupMenuButton<String>(
            onSelected: (period) {
              setState(() => _selectedPeriod = period);
              _loadProgressData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7', child: Text('آخر 7 أيام')),
              const PopupMenuItem(value: '30', child: Text('آخر 30 يوم')),
              const PopupMenuItem(value: '90', child: Text('آخر 3 أشهر')),
              const PopupMenuItem(value: '365', child: Text('آخر سنة')),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStatsTab(),
          _buildWeightTab(),
          _buildWorkoutTab(),
        ],
      ),
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatsTab() {
    if (_progressStats == null) {
      return _buildEmptyState('لا توجد بيانات كافية', 'ابدأ بإضافة قياسات الوزن لرؤية التقدم');
    }

    return RefreshIndicator(
      onRefresh: _loadProgressData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة التقدم العام
            _buildProgressOverviewCard(),

            const SizedBox(height: AppConstants.paddingMedium),

            // بطاقة إحصائيات التمارين
            _buildWorkoutStatsCard(),

            const SizedBox(height: AppConstants.paddingMedium),

            // بطاقة الأهداف
            _buildGoalsCard(),
          ],
        ),
      ),
    );
  }

  /// تبويب الوزن
  Widget _buildWeightTab() {
    if (_chartData == null || _chartData!.weightData.isEmpty) {
      return _buildEmptyState('لا توجد قياسات وزن', 'ابدأ بإضافة قياسات الوزن لرؤية الرسم البياني');
    }

    return RefreshIndicator(
      onRefresh: _loadProgressData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني للوزن
            _buildWeightChart(),

            const SizedBox(height: AppConstants.paddingMedium),

            // قائمة قياسات الوزن الأخيرة
            _buildRecentWeightEntries(),
          ],
        ),
      ),
    );
  }

  /// تبويب التمارين
  Widget _buildWorkoutTab() {
    if (_chartData == null || _chartData!.workoutData.isEmpty) {
      return _buildEmptyState('لا توجد سجلات تمارين', 'ابدأ بتسجيل التمارين لرؤية الإحصائيات');
    }

    return RefreshIndicator(
      onRefresh: _loadProgressData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني للتمارين
            _buildWorkoutChart(),

            const SizedBox(height: AppConstants.paddingMedium),

            // رسم بياني للسعرات المحروقة
            _buildCaloriesChart(),
          ],
        ),
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          ElevatedButton.icon(
            onPressed: _addWeightEntry,
            icon: const Icon(Icons.add),
            label: const Text('إضافة قياس وزن'),
          ),
        ],
      ),
    );
  }

  /// بطاقة نظرة عامة على التقدم
  Widget _buildProgressOverviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نظرة عامة على التقدم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            // شريط التقدم
            LinearProgressIndicator(
              value: _progressStats!.progressPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              '${_progressStats!.progressPercentage.toStringAsFixed(1)}% من الهدف',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الوزن الحالي',
                    '${_progressStats!.currentWeight.toStringAsFixed(1)} كجم',
                    Icons.monitor_weight,
                    AppConstants.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الوزن المفقود',
                    '${_progressStats!.weightLost.toStringAsFixed(1)} كجم',
                    Icons.trending_down,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الهدف المتبقي',
                    '${_progressStats!.weightToGo.toStringAsFixed(1)} كجم',
                    Icons.flag,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'فقدان أسبوعي',
                    '${_progressStats!.weeklyWeightLoss.toStringAsFixed(2)} كجم',
                    Icons.calendar_today,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة إحصائيات التمارين
  Widget _buildWorkoutStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات التمارين',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي التمارين',
                    '${_progressStats!.totalWorkouts}',
                    Icons.fitness_center,
                    AppConstants.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'السعرات المحروقة',
                    '${AppHelpers.formatNumber(_progressStats!.totalCaloriesBurned)}',
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            _buildStatItem(
              'متوسط مدة التمرين',
              '${_progressStats!.averageWorkoutDuration} دقيقة',
              Icons.timer,
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة الأهداف
  Widget _buildGoalsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأهداف والإنجازات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildGoalItem(
              'الوزن المستهدف',
              '${_progressStats!.targetWeight.toStringAsFixed(1)} كجم',
              _progressStats!.currentWeight <= _progressStats!.targetWeight,
            ),

            _buildGoalItem(
              'آخر قياس وزن',
              AppHelpers.formatDate(_progressStats!.lastWeightEntry),
              DateTime.now().difference(_progressStats!.lastWeightEntry).inDays <= 7,
            ),

            _buildGoalItem(
              'آخر تمرين',
              AppHelpers.formatDate(_progressStats!.lastWorkout),
              DateTime.now().difference(_progressStats!.lastWorkout).inDays <= 3,
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر إحصائية
  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// عنصر هدف
  Widget _buildGoalItem(String title, String value, bool achieved) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            achieved ? Icons.check_circle : Icons.radio_button_unchecked,
            color: achieved ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(title),
          ),
          Text(
            value,
            style: TextStyle(
              color: achieved ? Colors.green : AppConstants.textSecondaryColor,
              fontWeight: achieved ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// رسم بياني للوزن
  Widget _buildWeightChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تطور الوزن',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Container(
              height: 200,
              child: Center(
                child: Text(
                  'رسم بياني للوزن\n(سيتم تطويره باستخدام fl_chart)',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قائمة قياسات الوزن الأخيرة
  Widget _buildRecentWeightEntries() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'القياسات الأخيرة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'قائمة قياسات الوزن الأخيرة ستظهر هنا',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// رسم بياني للتمارين
  Widget _buildWorkoutChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نشاط التمارين',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Container(
              height: 200,
              child: Center(
                child: Text(
                  'رسم بياني للتمارين\n(سيتم تطويره باستخدام fl_chart)',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// رسم بياني للسعرات المحروقة
  Widget _buildCaloriesChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'السعرات المحروقة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Container(
              height: 200,
              child: Center(
                child: Text(
                  'رسم بياني للسعرات المحروقة\n(سيتم تطويره باستخدام fl_chart)',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
