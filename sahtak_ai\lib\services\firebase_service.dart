import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/workout_model.dart';
import '../models/meal_model.dart';
import '../models/recipe_model.dart';

/// خدمة Firebase الأساسية لإدارة البيانات
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _auth.currentUser?.uid;

  // ==================== إدارة المستخدمين ====================

  /// حفظ بيانات المستخدم
  Future<void> saveUserProfile(UserModel user) async {
    try {
      await _firestore
          .collection('users')
          .doc(user.id)
          .set(user.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ بيانات المستخدم: $e');
    }
  }

  /// الحصول على بيانات المستخدم
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب بيانات المستخدم: $e');
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUserProfile(String userId, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = Timestamp.now();
      await _firestore
          .collection('users')
          .doc(userId)
          .update(updates);
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: $e');
    }
  }

  // ==================== إدارة خطط التمارين ====================

  /// حفظ خطة التمارين
  Future<void> saveWorkoutPlan(WorkoutPlan plan) async {
    try {
      await _firestore
          .collection('workout_plans')
          .doc(plan.id)
          .set(plan.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ خطة التمارين: $e');
    }
  }

  /// الحصول على خطة التمارين النشطة للمستخدم
  Future<WorkoutPlan?> getActiveWorkoutPlan(String userId) async {
    try {
      final query = await _firestore
          .collection('workout_plans')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return WorkoutPlan.fromMap(query.docs.first.data());
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب خطة التمارين: $e');
    }
  }

  /// الحصول على جميع خطط التمارين للمستخدم
  Future<List<WorkoutPlan>> getUserWorkoutPlans(String userId) async {
    try {
      final query = await _firestore
          .collection('workout_plans')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => WorkoutPlan.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب خطط التمارين: $e');
    }
  }

  // ==================== إدارة خطط الوجبات ====================

  /// حفظ خطة الوجبات اليومية
  Future<void> saveDailyMealPlan(DailyMealPlan plan) async {
    try {
      await _firestore
          .collection('meal_plans')
          .doc(plan.id)
          .set(plan.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ خطة الوجبات: $e');
    }
  }

  /// الحصول على خطة الوجبات لتاريخ معين
  Future<DailyMealPlan?> getDailyMealPlan(String userId, DateTime date) async {
    try {
      final query = await _firestore
          .collection('meal_plans')
          .where('userId', isEqualTo: userId)
          .where('date', isEqualTo: Timestamp.fromDate(DateTime(date.year, date.month, date.day)))
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return DailyMealPlan.fromMap(query.docs.first.data());
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب خطة الوجبات: $e');
    }
  }

  /// الحصول على خطط الوجبات لفترة معينة
  Future<List<DailyMealPlan>> getMealPlansForPeriod(
    String userId,
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final query = await _firestore
          .collection('meal_plans')
          .where('userId', isEqualTo: userId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date')
          .get();

      return query.docs
          .map((doc) => DailyMealPlan.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب خطط الوجبات: $e');
    }
  }

  // ==================== إدارة الوصفات ====================

  /// الحصول على الوصفات المميزة
  Future<List<Recipe>> getFeaturedRecipes({int limit = 10}) async {
    try {
      final query = await _firestore
          .collection('recipes')
          .where('isFeatured', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(limit)
          .get();

      return query.docs
          .map((doc) => Recipe.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الوصفات المميزة: $e');
    }
  }

  /// البحث في الوصفات
  Future<List<Recipe>> searchRecipes({
    String? query,
    String? category,
    String? dietType,
    String? difficulty,
    int limit = 20,
  }) async {
    try {
      Query<Map<String, dynamic>> firestoreQuery = _firestore.collection('recipes');

      if (category != null && category.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('category', isEqualTo: category);
      }

      if (dietType != null && dietType.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('dietTypes', arrayContains: dietType);
      }

      if (difficulty != null && difficulty.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('difficulty', isEqualTo: difficulty);
      }

      firestoreQuery = firestoreQuery
          .orderBy('rating', descending: true)
          .limit(limit);

      final result = await firestoreQuery.get();

      List<Recipe> recipes = result.docs
          .map((doc) => Recipe.fromMap(doc.data()))
          .toList();

      // تطبيق البحث النصي محلياً
      if (query != null && query.isNotEmpty) {
        recipes = recipes.where((recipe) =>
          recipe.name.toLowerCase().contains(query.toLowerCase()) ||
          recipe.description.toLowerCase().contains(query.toLowerCase()) ||
          recipe.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()))
        ).toList();
      }

      return recipes;
    } catch (e) {
      throw Exception('فشل في البحث عن الوصفات: $e');
    }
  }

  /// الحصول على وصفة بالمعرف
  Future<Recipe?> getRecipeById(String recipeId) async {
    try {
      final doc = await _firestore
          .collection('recipes')
          .doc(recipeId)
          .get();

      if (doc.exists) {
        return Recipe.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب الوصفة: $e');
    }
  }

  // ==================== إدارة المفضلة ====================

  /// إضافة وصفة للمفضلة
  Future<void> addToFavorites(String userId, String recipeId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .set({
        'recipeId': recipeId,
        'addedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('فشل في إضافة الوصفة للمفضلة: $e');
    }
  }

  /// إزالة وصفة من المفضلة
  Future<void> removeFromFavorites(String userId, String recipeId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .delete();
    } catch (e) {
      throw Exception('فشل في إزالة الوصفة من المفضلة: $e');
    }
  }

  /// الحصول على الوصفات المفضلة
  Future<List<Recipe>> getFavoriteRecipes(String userId) async {
    try {
      final favoritesQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();

      if (favoritesQuery.docs.isEmpty) {
        return [];
      }

      final recipeIds = favoritesQuery.docs
          .map((doc) => doc.data()['recipeId'] as String)
          .toList();

      // جلب الوصفات بناءً على المعرفات
      final recipes = <Recipe>[];
      for (final recipeId in recipeIds) {
        final recipe = await getRecipeById(recipeId);
        if (recipe != null) {
          recipes.add(recipe);
        }
      }

      return recipes;
    } catch (e) {
      throw Exception('فشل في جلب الوصفات المفضلة: $e');
    }
  }

  /// التحقق من كون الوصفة في المفضلة
  Future<bool> isRecipeFavorite(String userId, String recipeId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .get();

      return doc.exists;
    } catch (e) {
      return false;
    }
  }
}
