import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/constants.dart';
import '../../utils/validators.dart';
import '../../utils/helpers.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../models/user_model.dart';
import '../home/<USER>';

/// شاشة جمع بيانات المستخدم الأساسية
class UserDataScreen extends StatefulWidget {
  const UserDataScreen({super.key});

  @override
  State<UserDataScreen> createState() => _UserDataScreenState();
}

class _UserDataScreenState extends State<UserDataScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  
  // متحكمات النصوص
  final _heightController = TextEditingController();
  final _currentWeightController = TextEditingController();
  final _targetWeightController = TextEditingController();
  
  // البيانات
  String _gender = '';
  DateTime? _birthDate;
  String _goal = '';
  String _activityLevel = '';
  String _dietType = '';
  List<String> _allergies = [];
  int _mealsPerDay = 3;
  
  int _currentPage = 0;
  bool _isLoading = false;

  @override
  void dispose() {
    _heightController.dispose();
    _currentWeightController.dispose();
    _targetWeightController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  /// الانتقال للصفحة التالية
  void _nextPage() {
    if (_currentPage < 4) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _saveUserData();
    }
  }

  /// الانتقال للصفحة السابقة
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// حفظ بيانات المستخدم
  Future<void> _saveUserData() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_validateCurrentPage()) return;

    setState(() => _isLoading = true);

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();
      final user = authService.currentUser;

      if (user != null) {
        final userModel = UserModel(
          id: user.uid,
          name: user.displayName ?? 'مستخدم',
          email: user.email ?? '',
          photoUrl: user.photoURL,
          gender: _gender,
          birthDate: _birthDate!,
          height: double.parse(_heightController.text),
          currentWeight: double.parse(_currentWeightController.text),
          targetWeight: double.parse(_targetWeightController.text),
          goal: _goal,
          activityLevel: _activityLevel,
          dietType: _dietType,
          allergies: _allergies,
          mealsPerDay: _mealsPerDay,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await firebaseService.saveUserProfile(userModel);
        
        // تحديث حالة أول تشغيل
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(AppConstants.keyFirstLaunch, false);

        if (mounted) {
          AppHelpers.showSuccessMessage(context, 'تم حفظ بياناتك بنجاح!');
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showErrorMessage(context, 'فشل في حفظ البيانات: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// التحقق من صحة البيانات في الصفحة الحالية
  bool _validateCurrentPage() {
    switch (_currentPage) {
      case 0:
        return _gender.isNotEmpty && _birthDate != null;
      case 1:
        return _heightController.text.isNotEmpty &&
               _currentWeightController.text.isNotEmpty &&
               _targetWeightController.text.isNotEmpty;
      case 2:
        return _goal.isNotEmpty;
      case 3:
        return _activityLevel.isNotEmpty;
      case 4:
        return _dietType.isNotEmpty;
      default:
        return true;
    }
  }

  /// اختيار تاريخ الميلاد
  Future<void> _selectBirthDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 13)),
      locale: const Locale('ar', 'SA'),
    );
    
    if (date != null) {
      setState(() => _birthDate = date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد الملف الشخصي'),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          // مؤشر التقدم
          LinearProgressIndicator(
            value: (_currentPage + 1) / 5,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
          ),
          
          // محتوى الصفحات
          Expanded(
            child: Form(
              key: _formKey,
              child: PageView(
                controller: _pageController,
                onPageChanged: (page) => setState(() => _currentPage = page),
                children: [
                  _buildPersonalInfoPage(),
                  _buildPhysicalDataPage(),
                  _buildGoalPage(),
                  _buildActivityPage(),
                  _buildDietPage(),
                ],
              ),
            ),
          ),
          
          // أزرار التنقل
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                if (_currentPage > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousPage,
                      child: const Text('السابق'),
                    ),
                  ),
                
                if (_currentPage > 0) const SizedBox(width: AppConstants.paddingMedium),
                
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _nextPage,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(_currentPage == 4 ? 'إنهاء' : 'التالي'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صفحة المعلومات الشخصية
  Widget _buildPersonalInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الشخصية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // اختيار الجنس
          Text(
            'الجنس',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('ذكر'),
                  value: 'ذكر',
                  groupValue: _gender,
                  onChanged: (value) => setState(() => _gender = value!),
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('أنثى'),
                  value: 'أنثى',
                  groupValue: _gender,
                  onChanged: (value) => setState(() => _gender = value!),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // تاريخ الميلاد
          Text(
            'تاريخ الميلاد',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          InkWell(
            onTap: _selectBirthDate,
            child: Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    _birthDate != null
                        ? AppHelpers.formatDate(_birthDate!)
                        : 'اختر تاريخ الميلاد',
                    style: TextStyle(
                      color: _birthDate != null ? Colors.black : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// صفحة البيانات الجسدية
  Widget _buildPhysicalDataPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'البيانات الجسدية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // الطول
          TextFormField(
            controller: _heightController,
            keyboardType: TextInputType.number,
            validator: AppValidators.validateHeight,
            decoration: const InputDecoration(
              labelText: 'الطول (سم)',
              hintText: 'مثال: 170',
              suffixText: 'سم',
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // الوزن الحالي
          TextFormField(
            controller: _currentWeightController,
            keyboardType: TextInputType.number,
            validator: AppValidators.validateWeight,
            decoration: const InputDecoration(
              labelText: 'الوزن الحالي (كجم)',
              hintText: 'مثال: 70',
              suffixText: 'كجم',
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // الوزن المستهدف
          TextFormField(
            controller: _targetWeightController,
            keyboardType: TextInputType.number,
            validator: AppValidators.validateWeight,
            decoration: const InputDecoration(
              labelText: 'الوزن المستهدف (كجم)',
              hintText: 'مثال: 65',
              suffixText: 'كجم',
            ),
          ),
        ],
      ),
    );
  }

  /// صفحة الهدف
  Widget _buildGoalPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ما هو هدفك؟',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          ...AppConstants.userGoals.map((goal) => Card(
            child: RadioListTile<String>(
              title: Text(goal),
              value: goal,
              groupValue: _goal,
              onChanged: (value) => setState(() => _goal = value!),
            ),
          )),
        ],
      ),
    );
  }

  /// صفحة مستوى النشاط
  Widget _buildActivityPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ما هو مستوى نشاطك؟',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          ...AppConstants.activityLevels.map((level) => Card(
            child: RadioListTile<String>(
              title: Text(level),
              value: level,
              groupValue: _activityLevel,
              onChanged: (value) => setState(() => _activityLevel = value!),
            ),
          )),
        ],
      ),
    );
  }

  /// صفحة نوع الدايت
  Widget _buildDietPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نوع النظام الغذائي',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          ...AppConstants.dietTypes.map((diet) => Card(
            child: RadioListTile<String>(
              title: Text(diet),
              value: diet,
              groupValue: _dietType,
              onChanged: (value) => setState(() => _dietType = value!),
            ),
          )),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // عدد الوجبات
          Text(
            'عدد الوجبات اليومية',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Slider(
            value: _mealsPerDay.toDouble(),
            min: 3,
            max: 6,
            divisions: 3,
            label: '$_mealsPerDay وجبات',
            onChanged: (value) => setState(() => _mealsPerDay = value.round()),
          ),
        ],
      ),
    );
  }
}
