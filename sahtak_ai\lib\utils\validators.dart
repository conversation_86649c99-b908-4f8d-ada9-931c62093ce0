/// مدققات البيانات للتطبيق
class AppValidators {
  
  /// التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }
    
    return null;
  }

  /// التحقق من صحة كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    return null;
  }

  /// التحقق من صحة كلمة المرور القوية
  static String? validateStrongPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    
    if (value.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير';
    }
    
    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير';
    }
    
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على رقم';
    }
    
    return null;
  }

  /// التحقق من تطابق كلمة المرور
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'يرجى تأكيد كلمة المرور';
    }
    
    if (value != password) {
      return 'كلمة المرور غير متطابقة';
    }
    
    return null;
  }

  /// التحقق من صحة الاسم
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الاسم';
    }
    
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    
    if (value.trim().length > 50) {
      return 'الاسم طويل جداً';
    }
    
    return null;
  }

  /// التحقق من صحة العمر
  static String? validateAge(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال العمر';
    }
    
    final age = int.tryParse(value);
    if (age == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (age < 13) {
      return 'العمر يجب أن يكون 13 سنة على الأقل';
    }
    
    if (age > 100) {
      return 'العمر يجب أن يكون أقل من 100 سنة';
    }
    
    return null;
  }

  /// التحقق من صحة الوزن
  static String? validateWeight(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الوزن';
    }
    
    final weight = double.tryParse(value);
    if (weight == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (weight < 30) {
      return 'الوزن يجب أن يكون 30 كيلو على الأقل';
    }
    
    if (weight > 300) {
      return 'الوزن يجب أن يكون أقل من 300 كيلو';
    }
    
    return null;
  }

  /// التحقق من صحة الطول
  static String? validateHeight(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الطول';
    }
    
    final height = double.tryParse(value);
    if (height == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (height < 100) {
      return 'الطول يجب أن يكون 100 سم على الأقل';
    }
    
    if (height > 250) {
      return 'الطول يجب أن يكون أقل من 250 سم';
    }
    
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }
    
    // إزالة المسافات والرموز
    final cleanNumber = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (cleanNumber.length < 10) {
      return 'رقم الهاتف قصير جداً';
    }
    
    if (cleanNumber.length > 15) {
      return 'رقم الهاتف طويل جداً';
    }
    
    return null;
  }

  /// التحقق من صحة النص المطلوب
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    return null;
  }

  /// التحقق من صحة النص مع حد أدنى للطول
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    
    if (value.trim().length < minLength) {
      return '$fieldName يجب أن يكون $minLength أحرف على الأقل';
    }
    
    return null;
  }

  /// التحقق من صحة النص مع حد أقصى للطول
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.trim().length > maxLength) {
      return '$fieldName يجب أن يكون أقل من $maxLength حرف';
    }
    
    return null;
  }

  /// التحقق من صحة الرقم في نطاق معين
  static String? validateNumberRange(String? value, double min, double max, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (number < min || number > max) {
      return '$fieldName يجب أن يكون بين $min و $max';
    }
    
    return null;
  }

  /// التحقق من صحة الرقم الصحيح
  static String? validateInteger(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    
    final number = int.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    return null;
  }

  /// التحقق من صحة الرقم العشري
  static String? validateDouble(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    return null;
  }

  /// التحقق من صحة تاريخ الميلاد
  static String? validateBirthDate(DateTime? value) {
    if (value == null) {
      return 'يرجى اختيار تاريخ الميلاد';
    }
    
    final now = DateTime.now();
    final age = now.year - value.year;
    
    if (value.isAfter(now)) {
      return 'تاريخ الميلاد لا يمكن أن يكون في المستقبل';
    }
    
    if (age < 13) {
      return 'العمر يجب أن يكون 13 سنة على الأقل';
    }
    
    if (age > 100) {
      return 'العمر يجب أن يكون أقل من 100 سنة';
    }
    
    return null;
  }

  /// التحقق من اختيار عنصر من قائمة
  static String? validateSelection(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى اختيار $fieldName';
    }
    return null;
  }
}
