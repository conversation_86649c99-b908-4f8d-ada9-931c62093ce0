import 'package:flutter/material.dart';

/// ثوابت التطبيق - صحتك AI
class AppConstants {
  // معلومات التطبيق
  static const String appName = 'صحتك AI';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'مساعد ذكي لإنشاء خطط تمارين رياضية ووجبات غذائية مخصصة';

  // ألوان التطبيق
  static const Color primaryColor = Color(0xFF2E7D32); // أخضر صحي
  static const Color secondaryColor = Color(0xFF4CAF50); // أخضر فاتح
  static const Color accentColor = Color(0xFFFF9800); // برتقالي للتحفيز
  static const Color backgroundColor = Color(0xFFF5F5F5); // رمادي فاتح
  static const Color cardColor = Colors.white;
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color successColor = Color(0xFF388E3C);

  // أحجام الخطوط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 20.0;
  static const double fontSizeXLarge = 24.0;

  // المسافات والحشو
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // نصف القطر للحواف
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;

  // أهداف المستخدم
  static const List<String> userGoals = [
    'خسارة الوزن',
    'بناء العضلات',
    'اللياقة العامة',
    'تحسين الصحة',
  ];

  // مستويات النشاط
  static const List<String> activityLevels = [
    'منخفض (قليل الحركة)',
    'متوسط (تمارين خفيفة)',
    'مرتفع (تمارين منتظمة)',
    'عالي جداً (رياضي)',
  ];

  // أنواع الدايت
  static const List<String> dietTypes = [
    'عادي',
    'نباتي',
    'كيتو',
    'قليل الكربوهيدرات',
    'البحر المتوسط',
  ];

  // أنواع التمارين
  static const List<String> workoutTypes = [
    'كارديو',
    'قوة',
    'مرونة',
    'يوجا',
    'بيلاتس',
  ];

  // أوقات الوجبات
  static const List<String> mealTimes = [
    'الإفطار',
    'سناك صباحي',
    'الغداء',
    'سناك مسائي',
    'العشاء',
  ];

  // مفاتيح التخزين المحلي
  static const String keyUserProfile = 'user_profile';
  static const String keyUserPlan = 'user_plan';
  static const String keyUserProgress = 'user_progress';
  static const String keyNotificationSettings = 'notification_settings';
  static const String keyFirstLaunch = 'first_launch';

  // رسائل الخطأ
  static const String errorGeneral = 'حدث خطأ غير متوقع';
  static const String errorNetwork = 'تحقق من اتصال الإنترنت';
  static const String errorAuth = 'خطأ في تسجيل الدخول';
  static const String errorValidation = 'يرجى التحقق من البيانات المدخلة';

  // رسائل النجاح
  static const String successLogin = 'تم تسجيل الدخول بنجاح';
  static const String successRegister = 'تم إنشاء الحساب بنجاح';
  static const String successUpdate = 'تم التحديث بنجاح';
  static const String successSave = 'تم الحفظ بنجاح';

  // إعدادات الإشعارات
  static const String notificationChannelId = 'sahtak_ai_notifications';
  static const String notificationChannelName = 'صحتك AI';
  static const String notificationChannelDescription = 'إشعارات التمارين والوجبات';

  // معرفات AdMob (يجب استبدالها بالمعرفات الحقيقية)
  static const String adMobAppId = 'ca-app-pub-3940256099942544~3347511713'; // Test ID
  static const String bannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111'; // Test ID
  static const String interstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712'; // Test ID
  static const String rewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917'; // Test ID

  // حدود البيانات
  static const int minAge = 13;
  static const int maxAge = 100;
  static const double minWeight = 30.0;
  static const double maxWeight = 300.0;
  static const double minHeight = 100.0;
  static const double maxHeight = 250.0;

  // إعدادات الخطة
  static const int defaultWorkoutDuration = 30; // دقيقة
  static const int defaultMealsPerDay = 3;
  static const int defaultCaloriesPerKg = 25; // سعرة لكل كيلو
}

/// ثيم التطبيق
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.green,
      primaryColor: AppConstants.primaryColor,
      scaffoldBackgroundColor: AppConstants.backgroundColor,
      fontFamily: 'Cairo', // خط عربي
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        color: AppConstants.cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: AppConstants.primaryColor),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
      ),
    );
  }
}
