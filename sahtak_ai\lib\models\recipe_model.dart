import 'package:cloud_firestore/cloud_firestore.dart';
import 'meal_model.dart';

/// نموذج الوصفة الصحية
class Recipe {
  final String id;
  final String name;
  final String description;
  final String category; // فطار، غداء، عشاء، حلويات، مشروبات
  final List<Ingredient> ingredients;
  final List<String> instructions;
  final NutritionInfo nutrition;
  final int preparationTime;
  final int cookingTime;
  final int servings;
  final String difficulty;
  final List<String> dietTypes;
  final List<String> allergens;
  final List<String> tags; // علامات للبحث
  final String? imageUrl;
  final List<String> images; // صور إضافية
  final String? videoUrl;
  final double rating; // التقييم من 1-5
  final int reviewsCount; // عدد التقييمات
  final String authorId; // معرف المؤلف
  final String authorName; // اسم المؤلف
  final bool isVerified; // وصفة معتمدة
  final bool isFeatured; // وصفة مميزة
  final int viewsCount; // عدد المشاهدات
  final int favoritesCount; // عدد الإعجابات
  final DateTime createdAt;
  final DateTime updatedAt;

  Recipe({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.ingredients,
    required this.instructions,
    required this.nutrition,
    required this.preparationTime,
    required this.cookingTime,
    required this.servings,
    required this.difficulty,
    required this.dietTypes,
    required this.allergens,
    required this.tags,
    this.imageUrl,
    required this.images,
    this.videoUrl,
    this.rating = 0.0,
    this.reviewsCount = 0,
    required this.authorId,
    required this.authorName,
    this.isVerified = false,
    this.isFeatured = false,
    this.viewsCount = 0,
    this.favoritesCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إجمالي وقت التحضير
  int get totalTime => preparationTime + cookingTime;

  /// السعرات لكل حصة
  int get caloriesPerServing => (nutrition.calories / servings).round();

  /// التحقق من توافق الوصفة مع نوع دايت معين
  bool isCompatibleWithDiet(String dietType) {
    return dietTypes.contains(dietType);
  }

  /// التحقق من وجود مسببات حساسية
  bool hasAllergen(String allergen) {
    return allergens.contains(allergen);
  }

  /// التحقق من توافق الوصفة مع قائمة الحساسيات
  bool isSafeForAllergies(List<String> userAllergies) {
    return !allergens.any((allergen) => userAllergies.contains(allergen));
  }

  /// تحويل الوصفة إلى وجبة
  Meal toMeal() {
    return Meal(
      id: id,
      name: name,
      description: description,
      type: category,
      ingredients: ingredients,
      instructions: instructions,
      nutrition: nutrition,
      preparationTime: preparationTime,
      cookingTime: cookingTime,
      servings: servings,
      difficulty: difficulty,
      dietTypes: dietTypes,
      allergens: allergens,
      imageUrl: imageUrl,
      createdAt: createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'ingredients': ingredients.map((i) => i.toMap()).toList(),
      'instructions': instructions,
      'nutrition': nutrition.toMap(),
      'preparationTime': preparationTime,
      'cookingTime': cookingTime,
      'servings': servings,
      'difficulty': difficulty,
      'dietTypes': dietTypes,
      'allergens': allergens,
      'tags': tags,
      'imageUrl': imageUrl,
      'images': images,
      'videoUrl': videoUrl,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'authorId': authorId,
      'authorName': authorName,
      'isVerified': isVerified,
      'isFeatured': isFeatured,
      'viewsCount': viewsCount,
      'favoritesCount': favoritesCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Recipe.fromMap(Map<String, dynamic> map) {
    return Recipe(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      ingredients: (map['ingredients'] as List<dynamic>?)
          ?.map((i) => Ingredient.fromMap(i as Map<String, dynamic>))
          .toList() ?? [],
      instructions: List<String>.from(map['instructions'] ?? []),
      nutrition: NutritionInfo.fromMap(map['nutrition'] ?? {}),
      preparationTime: map['preparationTime'] ?? 0,
      cookingTime: map['cookingTime'] ?? 0,
      servings: map['servings'] ?? 1,
      difficulty: map['difficulty'] ?? '',
      dietTypes: List<String>.from(map['dietTypes'] ?? []),
      allergens: List<String>.from(map['allergens'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      imageUrl: map['imageUrl'],
      images: List<String>.from(map['images'] ?? []),
      videoUrl: map['videoUrl'],
      rating: (map['rating'] ?? 0.0).toDouble(),
      reviewsCount: map['reviewsCount'] ?? 0,
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? '',
      isVerified: map['isVerified'] ?? false,
      isFeatured: map['isFeatured'] ?? false,
      viewsCount: map['viewsCount'] ?? 0,
      favoritesCount: map['favoritesCount'] ?? 0,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  Recipe copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    List<Ingredient>? ingredients,
    List<String>? instructions,
    NutritionInfo? nutrition,
    int? preparationTime,
    int? cookingTime,
    int? servings,
    String? difficulty,
    List<String>? dietTypes,
    List<String>? allergens,
    List<String>? tags,
    String? imageUrl,
    List<String>? images,
    String? videoUrl,
    double? rating,
    int? reviewsCount,
    String? authorId,
    String? authorName,
    bool? isVerified,
    bool? isFeatured,
    int? viewsCount,
    int? favoritesCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      nutrition: nutrition ?? this.nutrition,
      preparationTime: preparationTime ?? this.preparationTime,
      cookingTime: cookingTime ?? this.cookingTime,
      servings: servings ?? this.servings,
      difficulty: difficulty ?? this.difficulty,
      dietTypes: dietTypes ?? this.dietTypes,
      allergens: allergens ?? this.allergens,
      tags: tags ?? this.tags,
      imageUrl: imageUrl ?? this.imageUrl,
      images: images ?? this.images,
      videoUrl: videoUrl ?? this.videoUrl,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      isVerified: isVerified ?? this.isVerified,
      isFeatured: isFeatured ?? this.isFeatured,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Recipe(id: $id, name: $name, category: $category, difficulty: $difficulty, rating: $rating)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج تقييم الوصفة
class RecipeReview {
  final String id;
  final String recipeId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final List<String> images;
  final DateTime createdAt;

  RecipeReview({
    required this.id,
    required this.recipeId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.images,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'recipeId': recipeId,
      'userId': userId,
      'userName': userName,
      'rating': rating,
      'comment': comment,
      'images': images,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory RecipeReview.fromMap(Map<String, dynamic> map) {
    return RecipeReview(
      id: map['id'] ?? '',
      recipeId: map['recipeId'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      rating: (map['rating'] ?? 0.0).toDouble(),
      comment: map['comment'] ?? '',
      images: List<String>.from(map['images'] ?? []),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
}
