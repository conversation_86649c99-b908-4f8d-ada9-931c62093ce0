import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج تمرين واحد
class Exercise {
  final String name;
  final String description;
  final String type; // كارديو، قوة، مرونة
  final int duration; // بالدقائق
  final int? sets; // عدد المجموعات (للقوة)
  final int? reps; // عدد التكرارات (للقوة)
  final String? equipment; // المعدات المطلوبة
  final String difficulty; // سهل، متوسط، صعب
  final int caloriesBurned; // السعرات المحروقة التقديرية
  final String? imageUrl;
  final String? videoUrl;

  Exercise({
    required this.name,
    required this.description,
    required this.type,
    required this.duration,
    this.sets,
    this.reps,
    this.equipment,
    required this.difficulty,
    required this.caloriesBurned,
    this.imageUrl,
    this.videoUrl,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'type': type,
      'duration': duration,
      'sets': sets,
      'reps': reps,
      'equipment': equipment,
      'difficulty': difficulty,
      'caloriesBurned': caloriesBurned,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
    };
  }

  factory Exercise.fromMap(Map<String, dynamic> map) {
    return Exercise(
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: map['type'] ?? '',
      duration: map['duration'] ?? 0,
      sets: map['sets'],
      reps: map['reps'],
      equipment: map['equipment'],
      difficulty: map['difficulty'] ?? '',
      caloriesBurned: map['caloriesBurned'] ?? 0,
      imageUrl: map['imageUrl'],
      videoUrl: map['videoUrl'],
    );
  }
}

/// نموذج جلسة تمرين يومية
class WorkoutSession {
  final String id;
  final String name;
  final String description;
  final List<Exercise> exercises;
  final int totalDuration; // إجمالي الوقت بالدقائق
  final int totalCalories; // إجمالي السعرات المحروقة
  final String difficulty;
  final String type; // نوع التمرين الرئيسي
  final DateTime createdAt;

  WorkoutSession({
    required this.id,
    required this.name,
    required this.description,
    required this.exercises,
    required this.totalDuration,
    required this.totalCalories,
    required this.difficulty,
    required this.type,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'totalDuration': totalDuration,
      'totalCalories': totalCalories,
      'difficulty': difficulty,
      'type': type,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory WorkoutSession.fromMap(Map<String, dynamic> map) {
    return WorkoutSession(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      exercises: (map['exercises'] as List<dynamic>?)
          ?.map((e) => Exercise.fromMap(e as Map<String, dynamic>))
          .toList() ?? [],
      totalDuration: map['totalDuration'] ?? 0,
      totalCalories: map['totalCalories'] ?? 0,
      difficulty: map['difficulty'] ?? '',
      type: map['type'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
}

/// نموذج خطة التمارين الأسبوعية
class WorkoutPlan {
  final String id;
  final String userId;
  final String name;
  final String description;
  final Map<String, WorkoutSession?> weeklySchedule; // اليوم -> جلسة التمرين
  final DateTime startDate;
  final DateTime endDate;
  final String goal; // الهدف من الخطة
  final String difficulty;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  WorkoutPlan({
    required this.id,
    required this.userId,
    required this.name,
    required this.description,
    required this.weeklySchedule,
    required this.startDate,
    required this.endDate,
    required this.goal,
    required this.difficulty,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// الحصول على جلسة تمرين لتاريخ معين
  WorkoutSession? getWorkoutForDate(DateTime date) {
    final dayName = _getDayName(date.weekday);
    return weeklySchedule[dayName];
  }

  /// التحقق من وجود تمرين في يوم معين
  bool hasWorkoutOnDay(String day) {
    return weeklySchedule[day] != null;
  }

  /// حساب إجمالي التمارين في الأسبوع
  int get totalWorkoutsPerWeek {
    return weeklySchedule.values.where((workout) => workout != null).length;
  }

  /// حساب إجمالي الوقت الأسبوعي
  int get totalWeeklyDuration {
    return weeklySchedule.values
        .where((workout) => workout != null)
        .fold(0, (sum, workout) => sum + workout!.totalDuration);
  }

  /// حساب إجمالي السعرات المحروقة أسبوعياً
  int get totalWeeklyCalories {
    return weeklySchedule.values
        .where((workout) => workout != null)
        .fold(0, (sum, workout) => sum + workout!.totalCalories);
  }

  /// تحويل رقم اليوم إلى اسم اليوم
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'الاثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return 'الاثنين';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'description': description,
      'weeklySchedule': weeklySchedule.map(
        (key, value) => MapEntry(key, value?.toMap()),
      ),
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'goal': goal,
      'difficulty': difficulty,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory WorkoutPlan.fromMap(Map<String, dynamic> map) {
    return WorkoutPlan(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      weeklySchedule: (map['weeklySchedule'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(
          key,
          value != null ? WorkoutSession.fromMap(value as Map<String, dynamic>) : null,
        ),
      ) ?? {},
      startDate: (map['startDate'] as Timestamp).toDate(),
      endDate: (map['endDate'] as Timestamp).toDate(),
      goal: map['goal'] ?? '',
      difficulty: map['difficulty'] ?? '',
      isActive: map['isActive'] ?? false,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  WorkoutPlan copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    Map<String, WorkoutSession?>? weeklySchedule,
    DateTime? startDate,
    DateTime? endDate,
    String? goal,
    String? difficulty,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WorkoutPlan(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      weeklySchedule: weeklySchedule ?? this.weeklySchedule,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      goal: goal ?? this.goal,
      difficulty: difficulty ?? this.difficulty,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
