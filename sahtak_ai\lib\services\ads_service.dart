import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../utils/constants.dart';

/// خدمة إدارة الإعلانات باستخدام AdMob
class AdsService {
  static final AdsService _instance = AdsService._internal();
  factory AdsService() => _instance;
  AdsService._internal();

  bool _isInitialized = false;
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;

  // عدادات لتحديد متى يتم عرض الإعلانات
  int _screenViewCount = 0;
  int _actionCount = 0;

  /// تهيئة خدمة الإعلانات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await MobileAds.instance.initialize();
      _isInitialized = true;
      debugPrint('تم تهيئة خدمة الإعلانات بنجاح');

      // تحميل الإعلانات مسبقاً
      _loadInterstitialAd();
      _loadRewardedAd();
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإعلانات: $e');
    }
  }

  /// الحصول على معرف الإعلان البانر
  String get _bannerAdUnitId {
    if (kDebugMode) {
      return AppConstants.bannerAdUnitId; // Test ID
    }

    if (Platform.isAndroid) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    } else if (Platform.isIOS) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    }
    return AppConstants.bannerAdUnitId;
  }

  /// الحصول على معرف الإعلان البيني
  String get _interstitialAdUnitId {
    if (kDebugMode) {
      return AppConstants.interstitialAdUnitId; // Test ID
    }

    if (Platform.isAndroid) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    } else if (Platform.isIOS) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    }
    return AppConstants.interstitialAdUnitId;
  }

  /// الحصول على معرف الإعلان المكافئ
  String get _rewardedAdUnitId {
    if (kDebugMode) {
      return AppConstants.rewardedAdUnitId; // Test ID
    }

    if (Platform.isAndroid) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    } else if (Platform.isIOS) {
      return 'ca-app-pub-xxxxxxxxxxxxxxxx/yyyyyyyyyy'; // استبدل بمعرفك الحقيقي
    }
    return AppConstants.rewardedAdUnitId;
  }

  /// إنشاء إعلان بانر
  BannerAd createBannerAd({
    AdSize adSize = AdSize.banner,
    Function(Ad)? onAdLoaded,
    Function(Ad, LoadAdError)? onAdFailedToLoad,
  }) {
    return BannerAd(
      adUnitId: _bannerAdUnitId,
      size: adSize,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          debugPrint('تم تحميل إعلان البانر');
          onAdLoaded?.call(ad);
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('فشل في تحميل إعلان البانر: $error');
          ad.dispose();
          onAdFailedToLoad?.call(ad, error);
        },
        onAdOpened: (ad) {
          debugPrint('تم فتح إعلان البانر');
        },
        onAdClosed: (ad) {
          debugPrint('تم إغلاق إعلان البانر');
        },
      ),
    );
  }

  /// تحميل إعلان بيني
  Future<void> _loadInterstitialAd() async {
    await InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          debugPrint('تم تحميل الإعلان البيني');

          _interstitialAd!.setImmersiveMode(true);
          _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              debugPrint('تم عرض الإعلان البيني');
            },
            onAdDismissedFullScreenContent: (ad) {
              debugPrint('تم إغلاق الإعلان البيني');
              ad.dispose();
              _interstitialAd = null;
              // تحميل إعلان جديد
              _loadInterstitialAd();
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              debugPrint('فشل في عرض الإعلان البيني: $error');
              ad.dispose();
              _interstitialAd = null;
              _loadInterstitialAd();
            },
          );
        },
        onAdFailedToLoad: (error) {
          debugPrint('فشل في تحميل الإعلان البيني: $error');
          _interstitialAd = null;
        },
      ),
    );
  }

  /// عرض إعلان بيني
  Future<void> showInterstitialAd() async {
    if (_interstitialAd != null) {
      await _interstitialAd!.show();
    } else {
      debugPrint('الإعلان البيني غير جاهز');
      // محاولة تحميل إعلان جديد
      await _loadInterstitialAd();
    }
  }

  /// تحميل إعلان مكافئ
  Future<void> _loadRewardedAd() async {
    await RewardedAd.load(
      adUnitId: _rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          debugPrint('تم تحميل الإعلان المكافئ');

          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              debugPrint('تم عرض الإعلان المكافئ');
            },
            onAdDismissedFullScreenContent: (ad) {
              debugPrint('تم إغلاق الإعلان المكافئ');
              ad.dispose();
              _rewardedAd = null;
              // تحميل إعلان جديد
              _loadRewardedAd();
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              debugPrint('فشل في عرض الإعلان المكافئ: $error');
              ad.dispose();
              _rewardedAd = null;
              _loadRewardedAd();
            },
          );
        },
        onAdFailedToLoad: (error) {
          debugPrint('فشل في تحميل الإعلان المكافئ: $error');
          _rewardedAd = null;
        },
      ),
    );
  }

  /// عرض إعلان مكافئ
  Future<void> showRewardedAd({
    required OnUserEarnedRewardCallback onUserEarnedReward,
    Function()? onAdDismissed,
  }) async {
    if (_rewardedAd != null) {
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (ad) {
          debugPrint('تم عرض الإعلان المكافئ');
        },
        onAdDismissedFullScreenContent: (ad) {
          debugPrint('تم إغلاق الإعلان المكافئ');
          ad.dispose();
          _rewardedAd = null;
          onAdDismissed?.call();
          // تحميل إعلان جديد
          _loadRewardedAd();
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          debugPrint('فشل في عرض الإعلان المكافئ: $error');
          ad.dispose();
          _rewardedAd = null;
          _loadRewardedAd();
        },
      );

      await _rewardedAd!.show(onUserEarnedReward: onUserEarnedReward);
    } else {
      debugPrint('الإعلان المكافئ غير جاهز');
      // محاولة تحميل إعلان جديد
      await _loadRewardedAd();
    }
  }

  /// تتبع عرض الشاشات لعرض الإعلانات البينية
  void trackScreenView() {
    _screenViewCount++;

    // عرض إعلان بيني كل 3 شاشات
    if (_screenViewCount % 3 == 0) {
      showInterstitialAd();
    }
  }

  /// تتبع الإجراءات لعرض الإعلانات
  void trackAction() {
    _actionCount++;

    // عرض إعلان بيني كل 5 إجراءات
    if (_actionCount % 5 == 0) {
      showInterstitialAd();
    }
  }

  /// التحقق من توفر الإعلان البيني
  bool get isInterstitialAdReady => _interstitialAd != null;

  /// التحقق من توفر الإعلان المكافئ
  bool get isRewardedAdReady => _rewardedAd != null;

  /// تنظيف الموارد
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
  }
}

/// ويدجت إعلان البانر
class BannerAdWidget extends StatefulWidget {
  final AdSize adSize;
  final EdgeInsets? margin;

  const BannerAdWidget({
    super.key,
    this.adSize = AdSize.banner,
    this.margin,
  });

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  void _loadBannerAd() {
    _bannerAd = AdsService().createBannerAd(
      adSize: widget.adSize,
      onAdLoaded: (ad) {
        setState(() => _isAdLoaded = true);
      },
      onAdFailedToLoad: (ad, error) {
        setState(() => _isAdLoaded = false);
      },
    );

    _bannerAd!.load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAdLoaded || _bannerAd == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: widget.margin,
      width: _bannerAd!.size.width.toDouble(),
      height: _bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }
}

/// ويدجت زر الإعلان المكافئ
class RewardedAdButton extends StatelessWidget {
  final String text;
  final OnUserEarnedRewardCallback onRewardEarned;
  final VoidCallback? onPressed;
  final IconData? icon;

  const RewardedAdButton({
    super.key,
    required this.text,
    required this.onRewardEarned,
    this.onPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final adsService = AdsService();

    return ElevatedButton.icon(
      onPressed: adsService.isRewardedAdReady
          ? () async {
              await adsService.showRewardedAd(
                onUserEarnedReward: onRewardEarned,
                onAdDismissed: onPressed,
              );
            }
          : null,
      icon: Icon(icon ?? Icons.play_arrow),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
    );
  }
}
