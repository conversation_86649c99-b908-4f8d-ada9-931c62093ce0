import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../services/ai_service.dart';
import '../../models/user_model.dart';
import '../../models/workout_model.dart';
import '../../models/meal_model.dart';

/// شاشة الخطة الذكية
class AIPlanScreen extends StatefulWidget {
  const AIPlanScreen({super.key});

  @override
  State<AIPlanScreen> createState() => _AIPlanScreenState();
}

class _AIPlanScreenState extends State<AIPlanScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UserModel? _userProfile;
  WorkoutPlan? _workoutPlan;
  DailyMealPlan? _mealPlan;
  bool _isLoading = true;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();
      
      if (authService.currentUserId != null) {
        // تحميل بيانات المستخدم
        final profile = await firebaseService.getUserProfile(authService.currentUserId!);
        
        // تحميل خطة التمارين النشطة
        final workoutPlan = await firebaseService.getActiveWorkoutPlan(authService.currentUserId!);
        
        // تحميل خطة الوجبات لليوم
        final mealPlan = await firebaseService.getDailyMealPlan(authService.currentUserId!, DateTime.now());
        
        if (mounted) {
          setState(() {
            _userProfile = profile;
            _workoutPlan = workoutPlan;
            _mealPlan = mealPlan;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        AppHelpers.showErrorMessage(context, 'فشل في تحميل البيانات: $e');
      }
    }
  }

  /// توليد خطة جديدة
  Future<void> _generateNewPlan() async {
    if (_userProfile == null) return;

    setState(() => _isGenerating = true);

    try {
      final aiService = AIService();
      final firebaseService = FirebaseService();

      // توليد خطة التمارين
      final newWorkoutPlan = await aiService.generateWorkoutPlan(_userProfile!);
      await firebaseService.saveWorkoutPlan(newWorkoutPlan);

      // توليد خطة الوجبات لليوم
      final newMealPlan = await aiService.generateDailyMealPlan(_userProfile!, DateTime.now());
      await firebaseService.saveDailyMealPlan(newMealPlan);

      if (mounted) {
        setState(() {
          _workoutPlan = newWorkoutPlan;
          _mealPlan = newMealPlan;
        });
        AppHelpers.showSuccessMessage(context, 'تم إنشاء خطة جديدة بنجاح!');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showErrorMessage(context, 'فشل في إنشاء الخطة: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isGenerating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('خطتك الذكية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'التمارين', icon: Icon(Icons.fitness_center)),
            Tab(text: 'الوجبات', icon: Icon(Icons.restaurant_menu)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isGenerating ? null : _generateNewPlan,
          ),
        ],
      ),
      body: _isGenerating
          ? _buildGeneratingView()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildWorkoutTab(),
                _buildMealTab(),
              ],
            ),
    );
  }

  /// عرض توليد الخطة
  Widget _buildGeneratingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'جاري إنشاء خطتك المخصصة...',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'قد يستغرق هذا بضع ثوانٍ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب التمارين
  Widget _buildWorkoutTab() {
    if (_workoutPlan == null) {
      return _buildEmptyState(
        'لا توجد خطة تمارين',
        'اضغط على زر التحديث لإنشاء خطة جديدة',
        Icons.fitness_center,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الخطة
            _buildPlanInfoCard(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // جدول التمارين الأسبوعي
            _buildWeeklySchedule(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // تمرين اليوم
            _buildTodayWorkout(),
          ],
        ),
      ),
    );
  }

  /// تبويب الوجبات
  Widget _buildMealTab() {
    if (_mealPlan == null) {
      return _buildEmptyState(
        'لا توجد خطة وجبات',
        'اضغط على زر التحديث لإنشاء خطة جديدة',
        Icons.restaurant_menu,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ملخص السعرات
            _buildCaloriesSummary(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // وجبات اليوم
            _buildTodayMeals(),
          ],
        ),
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          ElevatedButton.icon(
            onPressed: _generateNewPlan,
            icon: const Icon(Icons.auto_awesome),
            label: const Text('إنشاء خطة جديدة'),
          ),
        ],
      ),
    );
  }

  /// بطاقة معلومات الخطة
  Widget _buildPlanInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _workoutPlan!.name,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              _workoutPlan!.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              children: [
                _buildInfoChip('${_workoutPlan!.totalWorkoutsPerWeek} تمارين/أسبوع'),
                const SizedBox(width: AppConstants.paddingSmall),
                _buildInfoChip('${_workoutPlan!.totalWeeklyDuration} دقيقة/أسبوع'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// رقاقة معلومات
  Widget _buildInfoChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppConstants.primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// الجدول الأسبوعي
  Widget _buildWeeklySchedule() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الجدول الأسبوعي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ..._workoutPlan!.weeklySchedule.entries.map((entry) {
              final day = entry.key;
              final workout = entry.value;
              
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: workout != null 
                      ? AppConstants.primaryColor 
                      : AppConstants.textSecondaryColor,
                  child: Icon(
                    workout != null ? Icons.fitness_center : Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(day),
                subtitle: Text(
                  workout != null 
                      ? '${workout.name} - ${workout.totalDuration} دقيقة'
                      : 'يوم راحة',
                ),
                trailing: workout != null 
                    ? const Icon(Icons.arrow_forward_ios, size: 16)
                    : null,
                onTap: workout != null ? () {
                  // TODO: فتح تفاصيل التمرين
                } : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  /// تمرين اليوم
  Widget _buildTodayWorkout() {
    final today = DateTime.now();
    final todayWorkout = _workoutPlan!.getWorkoutForDate(today);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تمرين اليوم',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            if (todayWorkout != null) ...[
              Text(
                todayWorkout.name,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '${todayWorkout.exercises.length} تمارين - ${todayWorkout.totalDuration} دقيقة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: بدء التمرين
                },
                icon: const Icon(Icons.play_arrow),
                label: const Text('بدء التمرين'),
              ),
            ] else ...[
              Text(
                'يوم راحة اليوم 🎉',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                'استمتع بيوم الراحة واستعد للتمرين القادم',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// ملخص السعرات
  Widget _buildCaloriesSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص السعرات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNutritionItem(
                  'السعرات',
                  '${_mealPlan!.totalCalories}',
                  'سعرة',
                  Colors.orange,
                ),
                _buildNutritionItem(
                  'البروتين',
                  '${_mealPlan!.totalNutrition.protein.toStringAsFixed(1)}',
                  'جم',
                  Colors.red,
                ),
                _buildNutritionItem(
                  'الكربوهيدرات',
                  '${_mealPlan!.totalNutrition.carbs.toStringAsFixed(1)}',
                  'جم',
                  Colors.blue,
                ),
                _buildNutritionItem(
                  'الدهون',
                  '${_mealPlan!.totalNutrition.fat.toStringAsFixed(1)}',
                  'جم',
                  Colors.green,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر تغذية
  Widget _buildNutritionItem(String title, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 4),
        Text.rich(
          TextSpan(
            text: value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            children: [
              TextSpan(
                text: ' $unit',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// وجبات اليوم
  Widget _buildTodayMeals() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'وجبات اليوم',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ..._mealPlan!.meals.entries.map((entry) {
          final mealType = entry.key;
          final meal = entry.value;
          
          if (meal == null) return const SizedBox.shrink();
          
          return Card(
            margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: AppConstants.primaryColor,
                child: Text(
                  mealType[0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              title: Text(meal.name),
              subtitle: Text('$mealType - ${meal.nutrition.calories} سعرة'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: فتح تفاصيل الوجبة
              },
            ),
          );
        }),
      ],
    );
  }
}
