# صحتك AI - مساعد ذكي للصحة واللياقة

تطبيق Android احترافي مطور باستخدام Flutter وFirebase يوفر خطط تمارين رياضية ووجبات غذائية مخصصة باستخدام تقنيات الذكاء الاصطناعي.

## 🎯 المميزات الرئيسية

### ✅ المميزات المنجزة
- **نظام المصادقة الكامل**: تسجيل الدخول بالبريد الإلكتروني وGoogle
- **جمع بيانات المستخدم**: واجهة تفاعلية لإدخال البيانات الشخصية والصحية
- **الذكاء الاصطناعي**: خوارزميات لتوليد خطط التمارين والوجبات المخصصة
- **واجهة مستخدم عربية**: تصميم حديث يدعم اللغة العربية بالكامل
- **تكامل Firebase**: حفظ واسترجاع البيانات من السحابة
- **بنية مشروع احترافية**: تنظيم الكود وفصل الطبقات

### 🚧 قيد التطوير
- شاشة تتبع التقدم مع الرسوم البيانية
- مكتبة الوصفات الصحية
- نظام الإشعارات
- دمج الإعلانات (AdMob)

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Flutter**: إطار العمل الرئيسي
- **Firebase**:
  - Authentication (المصادقة)
  - Firestore (قاعدة البيانات)
  - Cloud Messaging (الإشعارات)
  - Analytics (التحليلات)
- **Provider**: إدارة الحالة
- **Google Fonts**: الخطوط العربية
- **Google Sign-In**: تسجيل الدخول بـ Google

## 🚀 التشغيل والتطوير

### المتطلبات
- Flutter SDK (3.7.2 أو أحدث)
- Android Studio / VS Code
- حساب Firebase مع إعداد المشروع

### خطوات التشغيل
1. **تثبيت التبعيات**:
   ```bash
   flutter pub get
   ```

2. **إعداد Firebase**:
   - إنشاء مشروع جديد في Firebase Console
   - تفعيل Authentication, Firestore, Cloud Messaging
   - تحميل ملف `google-services.json` ووضعه في `android/app/`
   - تحديث معرفات التطبيق في الملف

3. **تشغيل التطبيق**:
   ```bash
   flutter run
   ```

## 📱 الشاشات المنجزة

### 1. شاشة البداية (Splash Screen)
- عرض لوجو التطبيق مع رسوم متحركة
- تحديد الشاشة التالية حسب حالة المستخدم

### 2. شاشات المصادقة
- **تسجيل الدخول**: بالبريد الإلكتروني أو Google
- **إنشاء حساب**: مع التحقق من البريد الإلكتروني

### 3. شاشة جمع البيانات (Onboarding)
- معلومات شخصية (الجنس، تاريخ الميلاد)
- بيانات جسدية (الطول، الوزن الحالي والمستهدف)
- الأهداف ومستوى النشاط
- نوع النظام الغذائي والحساسيات

### 4. الشاشة الرئيسية
- ملخص يومي للسعرات والتمارين
- إحصائيات سريعة (BMI، الوزن)
- إجراءات سريعة
- التقدم الأسبوعي

### 5. شاشة الخطة الذكية
- **تبويب التمارين**: جدول أسبوعي وتمرين اليوم
- **تبويب الوجبات**: ملخص السعرات ووجبات اليوم
- إمكانية توليد خطط جديدة

## 🤖 الذكاء الاصطناعي

### خوارزميات التوليد
- **خطط التمارين**: بناءً على الهدف ومستوى النشاط
- **خطط الوجبات**: حساب السعرات وتوزيع المغذيات
- **التخصيص**: مراعاة الحساسيات الغذائية ونوع الدايت

### المعادلات المستخدمة
- **BMI**: حساب مؤشر كتلة الجسم
- **Harris-Benedict**: حساب السعرات الحرارية المطلوبة
- **توزيع المغذيات**: بروتين، كربوهيدرات، دهون

## 📄 ملاحظات مهمة

**هذا إصدار MVP (Minimum Viable Product) يحتوي على:**
- ✅ البنية الأساسية الكاملة
- ✅ نظام المصادقة والتسجيل
- ✅ جمع بيانات المستخدم
- ✅ توليد الخطط الذكية
- ✅ واجهات المستخدم الأساسية
- 🚧 شاشات التقدم والوصفات (قيد التطوير)

**للحصول على تطبيق كامل وجاهز للنشر، يُنصح بإكمال:**
- شاشة تتبع التقدم مع الرسوم البيانية
- مكتبة الوصفات الصحية
- نظام الإشعارات
- دمج الإعلانات
- اختبارات شاملة
