import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../models/recipe_model.dart';

/// شاشة مكتبة الوصفات الصحية
class RecipesScreen extends StatefulWidget {
  const RecipesScreen({super.key});

  @override
  State<RecipesScreen> createState() => _RecipesScreenState();
}

class _RecipesScreenState extends State<RecipesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Recipe> _featuredRecipes = [];
  List<Recipe> _favoriteRecipes = [];
  List<Recipe> _searchResults = [];
  bool _isLoading = true;
  bool _isSearching = false;
  String _searchQuery = '';
  String _selectedCategory = '';
  String _selectedDietType = '';
  String _selectedDifficulty = '';

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRecipes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل الوصفات
  Future<void> _loadRecipes() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();

      // تحميل الوصفات المميزة
      final featured = await firebaseService.getFeaturedRecipes(limit: 20);

      // تحميل الوصفات المفضلة
      List<Recipe> favorites = [];
      if (authService.currentUserId != null) {
        favorites = await firebaseService.getFavoriteRecipes(authService.currentUserId!);
      }

      if (mounted) {
        setState(() {
          _featuredRecipes = featured;
          _favoriteRecipes = favorites;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        AppHelpers.showErrorMessage(context, 'فشل في تحميل الوصفات: $e');
      }
    }
  }

  /// البحث في الوصفات
  Future<void> _searchRecipes() async {
    if (_searchQuery.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() => _isSearching = true);

    try {
      final firebaseService = FirebaseService();
      final results = await firebaseService.searchRecipes(
        query: _searchQuery,
        category: _selectedCategory.isNotEmpty ? _selectedCategory : null,
        dietType: _selectedDietType.isNotEmpty ? _selectedDietType : null,
        difficulty: _selectedDifficulty.isNotEmpty ? _selectedDifficulty : null,
        limit: 50,
      );

      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isSearching = false);
        AppHelpers.showErrorMessage(context, 'فشل في البحث: $e');
      }
    }
  }

  /// إضافة/إزالة من المفضلة
  Future<void> _toggleFavorite(Recipe recipe) async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();

      if (authService.currentUserId == null) {
        AppHelpers.showErrorMessage(context, 'يجب تسجيل الدخول أولاً');
        return;
      }

      final isFavorite = await firebaseService.isRecipeFavorite(
        authService.currentUserId!,
        recipe.id,
      );

      if (isFavorite) {
        await firebaseService.removeFromFavorites(authService.currentUserId!, recipe.id);
        AppHelpers.showSuccessMessage(context, 'تم إزالة الوصفة من المفضلة');
      } else {
        await firebaseService.addToFavorites(authService.currentUserId!, recipe.id);
        AppHelpers.showSuccessMessage(context, 'تم إضافة الوصفة للمفضلة');
      }

      // إعادة تحميل المفضلة
      _loadRecipes();
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'فشل في تحديث المفضلة: $e');
    }
  }

  /// عرض فلاتر البحث
  void _showFilters() {
    showModalBottomSheet(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'فلترة الوصفات',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // فئة الوصفة
              Text('الفئة', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: AppConstants.paddingSmall),
              Wrap(
                spacing: 8,
                children: ['', 'إفطار', 'غداء', 'عشاء', 'حلويات', 'مشروبات']
                    .map((category) => FilterChip(
                          label: Text(category.isEmpty ? 'الكل' : category),
                          selected: _selectedCategory == category,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedCategory = selected ? category : '';
                            });
                          },
                        ))
                    .toList(),
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // نوع الدايت
              Text('نوع الدايت', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: AppConstants.paddingSmall),
              Wrap(
                spacing: 8,
                children: ['', 'عادي', 'نباتي', 'كيتو', 'قليل الكربوهيدرات']
                    .map((diet) => FilterChip(
                          label: Text(diet.isEmpty ? 'الكل' : diet),
                          selected: _selectedDietType == diet,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedDietType = selected ? diet : '';
                            });
                          },
                        ))
                    .toList(),
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // مستوى الصعوبة
              Text('مستوى الصعوبة', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: AppConstants.paddingSmall),
              Wrap(
                spacing: 8,
                children: ['', 'سهل', 'متوسط', 'صعب']
                    .map((difficulty) => FilterChip(
                          label: Text(difficulty.isEmpty ? 'الكل' : difficulty),
                          selected: _selectedDifficulty == difficulty,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedDifficulty = selected ? difficulty : '';
                            });
                          },
                        ))
                    .toList(),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          _selectedCategory = '';
                          _selectedDietType = '';
                          _selectedDifficulty = '';
                        });
                      },
                      child: const Text('إعادة تعيين'),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {});
                        Navigator.of(context).pop();
                        if (_searchQuery.isNotEmpty) {
                          _searchRecipes();
                        }
                      },
                      child: const Text('تطبيق'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('مكتبة الوصفات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المميزة', icon: Icon(Icons.star)),
            Tab(text: 'البحث', icon: Icon(Icons.search)),
            Tab(text: 'المفضلة', icon: Icon(Icons.favorite)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilters,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFeaturedTab(),
          _buildSearchTab(),
          _buildFavoritesTab(),
        ],
      ),
    );
  }

  /// تبويب الوصفات المميزة
  Widget _buildFeaturedTab() {
    if (_featuredRecipes.isEmpty) {
      return _buildEmptyState(
        'لا توجد وصفات مميزة',
        'سيتم إضافة وصفات مميزة قريباً',
        Icons.star_outline,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRecipes,
      child: GridView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: AppConstants.paddingMedium,
          mainAxisSpacing: AppConstants.paddingMedium,
        ),
        itemCount: _featuredRecipes.length,
        itemBuilder: (context, index) {
          return _buildRecipeCard(_featuredRecipes[index]);
        },
      ),
    );
  }

  /// تبويب البحث
  Widget _buildSearchTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن وصفة...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                          _searchResults = [];
                        });
                      },
                    )
                  : null,
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
              if (value.isNotEmpty) {
                _searchRecipes();
              } else {
                setState(() => _searchResults = []);
              }
            },
          ),
        ),

        // نتائج البحث
        Expanded(
          child: _isSearching
              ? const Center(child: CircularProgressIndicator())
              : _searchResults.isEmpty && _searchQuery.isNotEmpty
                  ? _buildEmptyState(
                      'لا توجد نتائج',
                      'جرب البحث بكلمات مختلفة',
                      Icons.search_off,
                    )
                  : _searchResults.isEmpty
                      ? _buildEmptyState(
                          'ابدأ البحث',
                          'اكتب اسم الوصفة أو المكون',
                          Icons.search,
                        )
                      : GridView.builder(
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.75,
                            crossAxisSpacing: AppConstants.paddingMedium,
                            mainAxisSpacing: AppConstants.paddingMedium,
                          ),
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            return _buildRecipeCard(_searchResults[index]);
                          },
                        ),
        ),
      ],
    );
  }

  /// تبويب المفضلة
  Widget _buildFavoritesTab() {
    if (_favoriteRecipes.isEmpty) {
      return _buildEmptyState(
        'لا توجد وصفات مفضلة',
        'أضف وصفات للمفضلة لتظهر هنا',
        Icons.favorite_outline,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRecipes,
      child: GridView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: AppConstants.paddingMedium,
          mainAxisSpacing: AppConstants.paddingMedium,
        ),
        itemCount: _favoriteRecipes.length,
        itemBuilder: (context, index) {
          return _buildRecipeCard(_favoriteRecipes[index]);
        },
      ),
    );
  }

  /// بطاقة الوصفة
  Widget _buildRecipeCard(Recipe recipe) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          // TODO: فتح تفاصيل الوصفة
          _showRecipeDetails(recipe);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الوصفة
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  image: recipe.imageUrl != null
                      ? DecorationImage(
                          image: NetworkImage(recipe.imageUrl!),
                          fit: BoxFit.cover,
                          onError: (error, stackTrace) {
                            // في حالة فشل تحميل الصورة
                          },
                        )
                      : null,
                ),
                child: recipe.imageUrl == null
                    ? const Icon(
                        Icons.restaurant_menu,
                        size: 48,
                        color: Colors.grey,
                      )
                    : Stack(
                        children: [
                          // زر المفضلة
                          Positioned(
                            top: 8,
                            right: 8,
                            child: CircleAvatar(
                              radius: 16,
                              backgroundColor: Colors.white.withOpacity(0.9),
                              child: IconButton(
                                icon: Icon(
                                  Icons.favorite,
                                  size: 16,
                                  color: _favoriteRecipes.contains(recipe)
                                      ? Colors.red
                                      : Colors.grey,
                                ),
                                onPressed: () => _toggleFavorite(recipe),
                              ),
                            ),
                          ),

                          // تقييم الوصفة
                          if (recipe.rating > 0)
                            Positioned(
                              bottom: 8,
                              left: 8,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.7),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.star,
                                      size: 12,
                                      color: Colors.amber,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      recipe.rating.toStringAsFixed(1),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
              ),
            ),

            // معلومات الوصفة
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الوصفة
                    Text(
                      recipe.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // معلومات سريعة
                    Row(
                      children: [
                        Icon(
                          Icons.timer,
                          size: 12,
                          color: AppConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${recipe.totalTime} د',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.local_fire_department,
                          size: 12,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${recipe.caloriesPerServing}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // نوع الدايت
                    if (recipe.dietTypes.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          recipe.dietTypes.first,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل الوصفة
  void _showRecipeDetails(Recipe recipe) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان الوصفة
                Text(
                  recipe.name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // معلومات سريعة
                Row(
                  children: [
                    _buildInfoChip('${recipe.totalTime} دقيقة', Icons.timer),
                    const SizedBox(width: AppConstants.paddingSmall),
                    _buildInfoChip('${recipe.caloriesPerServing} سعرة', Icons.local_fire_department),
                    const SizedBox(width: AppConstants.paddingSmall),
                    _buildInfoChip(recipe.difficulty, Icons.signal_cellular_alt),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // الوصف
                Text(
                  'الوصف',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(recipe.description),

                const SizedBox(height: AppConstants.paddingLarge),

                // المكونات
                Text(
                  'المكونات',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                ...recipe.ingredients.map((ingredient) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(Icons.circle, size: 6),
                      const SizedBox(width: 8),
                      Text('${ingredient.amount} ${ingredient.unit} ${ingredient.name}'),
                    ],
                  ),
                )),

                const SizedBox(height: AppConstants.paddingLarge),

                // خطوات التحضير
                Text(
                  'خطوات التحضير',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                ...recipe.instructions.asMap().entries.map((entry) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 12,
                        backgroundColor: AppConstants.primaryColor,
                        child: Text(
                          '${entry.key + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(child: Text(entry.value)),
                    ],
                  ),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// رقاقة معلومات
  Widget _buildInfoChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppConstants.primaryColor),
          const SizedBox(width: 4),
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
